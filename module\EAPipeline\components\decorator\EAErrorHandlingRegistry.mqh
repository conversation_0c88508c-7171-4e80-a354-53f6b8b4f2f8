#property strict

#include "../EARegistryBase.mqh"
#include "../EAErrorHandler.mqh"

#define EA_ERROR_HANDLING_REGISTRY_NAME "EAErrorHandlingRegistry"
#define EA_ERROR_HANDLING_REGISTRY_TYPE "EAErrorHandlingRegistry"

//+------------------------------------------------------------------+
//| EAErrorHandlingRegistry 類 - 錯誤處理裝飾者模式實現的 EARegistryBase |
//+------------------------------------------------------------------+
class EAErrorHandlingRegistry : public EARegistryBase
{
private:
    EAErrorHandler* m_error_handler;  // 錯誤處理器

public:
    // 構造函數
    EAErrorHandlingRegistry(ItemRegistry<void*>* registry, string type = EA_ERROR_HANDLING_REGISTRY_TYPE, EAErrorHandler* error_handler = NULL)
        : EARegistryBase(registry, type),
          m_error_handler(error_handler ? error_handler : EAErrorHandler::GetInstance())
    {
    }

    EAErrorHandlingRegistry(EARegistryBase* registry, string type = EA_ERROR_HANDLING_REGISTRY_TYPE, EAErrorHandler* error_handler = NULL)
        : EARegistryBase(registry, type),
          m_error_handler(error_handler ? error_handler : EAErrorHandler::GetInstance())
    {
        EARegistryBase::SetRegistry(registry.GetRegistry());
    }

    EAErrorHandlingRegistry(string name, string type = EA_ERROR_HANDLING_REGISTRY_TYPE, int maxItems = EA_REGISTRY_BASE_MAX_ITEMS, EAErrorHandler* error_handler = NULL)
        : EARegistryBase(name, type, maxItems),
          m_error_handler(error_handler ? error_handler : EAErrorHandler::GetInstance())
    {
    }

    // 析構函數
    virtual ~EAErrorHandlingRegistry()
    {
        // 不需要刪除 m_error_handler，因為它是單例
    }

    // 註冊新項目
    virtual RegistryResult<string>* Register(const string name, const string description, void* value) override
    {
        if(name == "")
        {
            string error_msg = "註冊項目失敗: 名稱不能為空";
            m_error_handler.HandleError(error_msg);
            return new RegistryResult<string>(false, error_msg, "", GetName());
        }

        RegistryResult<string>* result = EARegistryBase::Register(name, description, value);

        if(!result.IsSuccess())
        {
            string error_msg = "註冊項目失敗: " + name + ", 原因: " + result.GetMessage();
            m_error_handler.HandleError(error_msg);
        }

        return result;
    }

    // 移除項目
    virtual bool Unregister(const string key) override
    {
        if(key == "")
        {
            string error_msg = "移除項目失敗: 鍵不能為空";
            m_error_handler.HandleError(error_msg);
            return false;
        }

        bool result = EARegistryBase::Unregister(key);

        if(!result)
        {
            string error_msg = "移除項目失敗: " + key;
            m_error_handler.HandleError(error_msg);
        }

        return result;
    }

    // 清空註冊器
    virtual void Clear() override
    {
        EARegistryBase::Clear();
    }

    // 獲取註冊器名稱
    virtual string GetName() override
    {
        return EARegistryBase::GetName();
    }

    // 獲取項目數量
    virtual int GetCount() override
    {
        return EARegistryBase::GetCount();
    }

    // 獲取最大項目數量
    virtual int GetMaxItems() override
    {
        return EARegistryBase::GetMaxItems();
    }

    // 根據Key獲取項目
    virtual RegistryItem<void*>* GetItem(const string key) override
    {
        if(key == "")
        {
            string error_msg = "獲取項目失敗: 鍵不能為空";
            m_error_handler.HandleError(error_msg);
            return NULL;
        }

        RegistryItem<void*>* item = EARegistryBase::GetItem(key);

        if(item == NULL)
        {
            string error_msg = "獲取項目失敗: " + key;
            m_error_handler.HandleError(error_msg);
        }

        return item;
    }

    // 獲取所有Key
    virtual int GetAllKeys(string &Keys[]) override
    {
        return EARegistryBase::GetAllKeys(Keys);
    }

    // 獲取最後註冊的鍵
    virtual string GetLastRegisteredKey() override
    {
        return EARegistryBase::GetLastRegisteredKey();
    }

    // 獲取被裝飾的註冊器
    virtual ItemRegistry<void*>* GetRegistry() override
    {
        return EARegistryBase::GetRegistry();
    }

protected:
    // 設置被裝飾的註冊器
    virtual void SetRegistry(ItemRegistry<void*>* registry) override
    {
        EARegistryBase::SetRegistry(registry);
    }
};
