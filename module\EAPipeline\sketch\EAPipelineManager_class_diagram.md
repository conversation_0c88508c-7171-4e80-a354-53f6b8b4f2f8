# EAPipelineManager 類別圖

```mermaid
classDiagram
    class PipelineResult {
        -bool m_success
        -string m_message
        -string m_source
        +PipelineResult(bool success, string message, string source)
        +bool IsSuccess()
        +string GetMessage()
        +string GetSource()
    }

    class IPipeline {
        <<interface>>
        +void Execute()
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
    }

    class Pipeline {
        <<abstract>>
        -string m_name
        -string m_type
        -PipelineResult* m_result
        -bool m_executed
        +Pipeline(string name, string type)
        +~Pipeline()
        +virtual void Execute() = 0
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
        #void SetResult(bool success, string message, string source)
    }

    class EAPipeline {
        <<abstract>>
        +EAPipeline(string name)
        +~EAPipeline()
        +RegistryResult~string~* Register(string name, string description, void* value)
        +RegistryItem~void*~* GetItem(string name)
        +void Execute() virtual
    }

    class HashMap~Key, Value~ {
        +HashMap(EqualityComparer~Key~* comparer, bool owned)
        +~HashMap()
        +int size()
        +bool isEmpty()
        +bool contains(Key key)
        +bool remove(Key key)
        +void clear()
        +Value get(Key key, Value def)
        +void set(Key key, Value value)
        +bool keys(Collection~Key~& col)
        +bool values(Collection~Value~& col)
    }

    class EAPipelineManager {
        <<singleton>>
        -static EAPipelineManager* m_instance
        -HashMap~string, EAPipeline*~* m_pipelines
        -EAPipelineManager()
        -~EAPipelineManager()
        +static EAPipelineManager* GetInstance()
        +PipelineResult* AddPipeline(EAPipeline* pipeline)
        +PipelineResult* RemovePipeline(EAPipeline* pipeline)
        +PipelineResult* RemovePipelineByName(string name)
        +void Clear()
        +int GetPipelineCount()
        +int GetMaxPipelines()
        +PipelineResult* ExecutePipeline(string name)
        +void GetAllPipelineNames(string& names[])
        +EAPipeline* GetPipelineByName(string name)
    }

    IPipeline <|.. Pipeline
    Pipeline <|-- EAPipeline
    EAPipelineManager o-- HashMap~string, EAPipeline*~
    EAPipelineManager o-- EAPipeline
    EAPipelineManager ..> PipelineResult
```
