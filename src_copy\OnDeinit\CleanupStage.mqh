#property strict

#include "../../module/Pipeline/OnDeinitPipeline.mqh"
#include "../../module/Registry/PointerRegistry.mqh"

// 清理階段
class CleanupStage : public OnDeinitPipeline
{
public:
    bool Execute(int in = 0) override
    {
        // 獲取註冊器實例
        PointerRegistry<void*>* registry = PointerRegistry<void*>::GetInstance();
        
        // 清理註冊器中的所有項目
        registry.Clear();
        
        // 刪除所有對象
        ObjectsDeleteAll(0, -1, -1);
        
        Print("清理階段完成");
        return true;
    }
};
