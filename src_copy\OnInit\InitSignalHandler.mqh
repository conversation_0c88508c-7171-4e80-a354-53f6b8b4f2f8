#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/Trade/SignalHandler.mqh"
#include "../../module/Util/ErrorHandler.mqh"

// 初始化單一交易信號處理器階段
class InitSignleHandler : public OnInitStage
{
public:
    InitSignleHandler() : OnInitStage(ONINIT_STAGE_VARIABLE_INIT){}
    
    ENUM_INIT_RETCODE Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("初始化單一交易信號處理器階段");

        // 獲取錯誤處理器實例
        ErrorHandler* error_handler = ErrorHandler::GetInstance();
        if(error_handler == NULL) {
            Print("錯誤處理器未初始化");
            return INIT_FAILED;
        }

        if(!this.IsRegistered()) {
            error_handler.HandleError("初始化單一交易信號處理器階段註冊失敗");
            return INIT_FAILED;
        }
        
        // 創建單一交易信號處理器實例
        SignalHandler* single_handler = new SignalHandler();
        if(single_handler == NULL) {
            error_handler.HandleError("無法創建單一交易信號處理器實例");
            return INIT_FAILED;
        }
        
        // 註冊單一交易信號處理器
        if(!Register("SignalHandler", "單一交易信號處理器", single_handler, "SignalHandler"))
        {
            error_handler.HandleError("單一交易信號處理器註冊失敗");
            delete single_handler; // 釋放資源
            return INIT_FAILED;
        }

        Print("單一交易信號處理器初始化成功");
        return INIT_SUCCEEDED;
    }
}init_signle_handler_stage;
