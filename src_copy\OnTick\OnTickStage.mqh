#property strict

#include "../../module/Stage/OnTickStage.mqh"
#include "OrderCheck.mqh"
#include "SignalCheck.mqh"
#include "OperationSetting.mqh"
#include "TradeExecution.mqh"

// // OnTick 流水線處理器
// class TickPipelineProcessor : public OnTickPipelineManager
// {
// public:
//     TickPipelineProcessor(int magic_number = 12345, string symbol = NULL) {
//         // 設置註冊器的鍵字頭
//         GetRegistry().SetKeyPrefix("Trading");

//         // 交易品種
//         string trade_symbol = (symbol == NULL) ? Symbol() : symbol;

//         // 添加 OnTick 開始階段
//         AddPipeline(new OnTickStartStage());

//         // 添加市場數據更新階段
//         AddPipeline(new MarketDataStage(trade_symbol));

//         // 添加持倉狀態檢查階段
//         PositionCheckStage* positionCheck = new PositionCheckStage(magic_number, trade_symbol);
//         AddPipeline(positionCheck);

//         // 添加信號檢查階段
//         SignalCheckStage* signalCheck = new SignalCheckStage(trade_symbol);
//         AddPipeline(signalCheck);

//         // 添加風險控制檢查階段
//         RiskCheckStage* riskCheck = new RiskCheckStage(signalCheck, trade_symbol, magic_number);
//         AddPipeline(riskCheck);

//         // 添加交易執行階段
//         TradeExecutionStage* tradeExec = new TradeExecutionStage(signalCheck, riskCheck, trade_symbol, magic_number);
//         AddPipeline(tradeExec);

//         // 添加日誌記錄和通知階段
//         AddPipeline(new LoggingStage(signalCheck, tradeExec, trade_symbol));

//         // 添加 OnTick 結束階段
//         AddPipeline(new OnTickEndStage());
//     }
// };
