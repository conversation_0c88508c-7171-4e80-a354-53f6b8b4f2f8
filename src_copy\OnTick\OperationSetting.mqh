#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/Util/ErrorHandler.mqh"
#include "../../module/Trade/MqlStruct.mqh"
#include "../../module/Trade/SignalHandler.mqh"
#include "../../module/mql4-lib-master/Trade/OrderTracker.mqh"

// 交易執行階段
class OperationSetting : public OnTickStage
{
public:
    // 建構函數
    OperationSetting() : OnTickStage(ONTICK_STAGE_RISK_CHECK) {}
    bool Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("交易執行階段");

        // 獲取錯誤處理器實例
        ErrorHandler* error_handler = ErrorHandler::GetInstance();
        if(error_handler == NULL) {
            Print("錯誤處理器未初始化");
            ExpertRemove();
        }

        // 獲取單一交易信號處理器實例
        SignalHandler* signal_handler = dynamic_cast<SignalHandler*>(GetRegistry().GetValue("SignalHandler", NULL));
        if(signal_handler == NULL) {
            error_handler.HandleError("單一交易信號處理器未初始化");
            ExpertRemove();
        }

        // 獲取 MQL開倉 封裝器實例
        MqlOrderWrapper* mql_order_wrapper = dynamic_cast<MqlOrderWrapper*>(GetRegistry().GetValue("MqlOrderWrapper", NULL));
        if(mql_order_wrapper == NULL) {
            error_handler.HandleError("MQL開倉 封裝器未初始化");
            ExpertRemove();
        }

        // 獲取 MQL平倉 封裝器實例
        MqlCloseWrapper* mql_close_wrapper = dynamic_cast<MqlCloseWrapper*>(GetRegistry().GetValue("MqlCloseWrapper", NULL));
        if(mql_close_wrapper == NULL) {
            error_handler.HandleError("MQL平倉 封裝器未初始化");
            ExpertRemove();
        }

        // 獲取訂單追蹤器實例
        OrderTracker* order_tracker = dynamic_cast<OrderTracker*>(GetRegistry().GetValue("OrderTracker", NULL));
        if(order_tracker == NULL) {
            error_handler.HandleError("訂單追蹤器未初始化");
            ExpertRemove();
        }

        // 獲取訂單配置
        MqlOrder order_config;
        MqlClose close_config;
        mql_order_wrapper.GetOrder(order_config);
        mql_close_wrapper.GetClose(close_config);
        // 獲取所有訂單
        Vector<Order*>* orders = new Vector<Order*>();
        order_tracker.getOrders(orders);
        Order* start_order;
        if(!orders.isEmpty())
            start_order = orders.get(0);

        //如果有買入信號
        if(signal_handler.GetSignal() == SIGNAL_BUY)
        {   
            order_config.Initialize();
            order_config.type = OP_BUY;
            order_config.volume = 0.01;
            order_config.price = Ask;
            order_config.slippage = 10;
            order_config.stoploss = 0.0;
            order_config.takeprofit = 0.0;
            order_config.comment = "OperationSetting";
            order_config.magic = 12345;
            order_config.arrowcolor = clrNONE;
            order_config.expiration = 0;
            if(!order_config.ValidateData())
            {
                error_handler.HandleError("設定開倉參數失敗");
                signal_handler.SetSignal(SIGNAL_NONE);
            }
            mql_order_wrapper.SetOrder(order_config);
        }
        //如果有賣出信號
        else if(signal_handler.GetSignal() == SIGNAL_SELL)
        {
            order_config.Initialize();
            order_config.type = OP_SELL;
            order_config.volume = 0.01;
            order_config.price = Bid;
            order_config.slippage = 10;
            order_config.stoploss = 0.0;
            order_config.takeprofit = 0.0;
            order_config.comment = "OperationSetting";
            order_config.magic = 12345;
            order_config.arrowcolor = clrNONE;
            order_config.expiration = 0;
            if(!order_config.ValidateData())
            {
                error_handler.HandleError("設定開倉參數失敗");
                signal_handler.SetSignal(SIGNAL_NONE);
            }
            mql_order_wrapper.SetOrder(order_config);
        }

        //如果有平倉信號
        if(signal_handler.GetSignal() == SIGNAL_CLOSE)
        {
            close_config.Initialize();
            close_config.ticket = start_order.getTicket();
            close_config.lots = start_order.getLots();
            close_config.price = start_order.getType() == OP_BUY?Bid:Ask;
            close_config.slippage = 10;
            close_config.arrowcolor = clrNONE;
            if(!close_config.ValidateData())
            {
                error_handler.HandleError("設定平倉參數失敗");
                signal_handler.SetSignal(SIGNAL_NONE);
            }
            mql_close_wrapper.SetClose(close_config);
        }

        return true;
    }
}operation_setting_stage;