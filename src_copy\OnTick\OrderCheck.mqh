#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/mql4-lib-master/Trade/OrderTracker.mqh"
#include "../../module/Util/ErrorHandler.mqh"

// 初始化訂單追蹤器階段
class OrderCheck : public OnTickStage
{
public:
    // 建構函數
    OrderCheck() : OnTickStage(ONTICK_STAGE_POSITION_CHECK) {}
    bool Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("訂單檢查階段");

        // 獲取錯誤處理器實例
        ErrorHandler* error_handler = ErrorHandler::GetInstance();
        if(error_handler == NULL) {
            Print("錯誤處理器未初始化");
            ExpertRemove();
        }

        OrderTracker* order_tracker = dynamic_cast<OrderTracker*>(GetRegistry().GetValue("OrderTracker", NULL));
        if(order_tracker == NULL) {
            error_handler.HandleError("訂單追蹤器未初始化");
            ExpertRemove();
        }

        order_tracker.track();
        return true;
    }
}order_check_stage;
