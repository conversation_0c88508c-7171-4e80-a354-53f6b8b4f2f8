#property strict

#include "../../module/Stage/Stage.mqh"
#include "../../module/mql4-lib-master/Trade/OrderManager.mqh"
#include "../../module/Util/ErrorHandler.mqh"
#include "../../module/Trade/MqlStruct.mqh"
#include "../../module/Trade/SignalHandler.mqh"

// 初始化訂單追蹤器階段
class TradeExecution : public OnTickStage
{
public:
    // 建構函數
    TradeExecution() : OnTickStage(ONTICK_STAGE_TRADE_EXECUTION) {}
    bool Execute(void* in = NULL) override { // 實現 Execute 方法
        Print("交易執行階段");

        // 獲取錯誤處理器實例
        ErrorHandler* error_handler = ErrorHandler::GetInstance();
        if(error_handler == NULL) {
            Print("錯誤處理器未初始化");
            ExpertRemove();
        }

        // 獲取訂單管理器實例
        OrderManager* order_manager = dynamic_cast<OrderManager*>(GetRegistry().GetValue("OrderManager", NULL));
        if(order_manager == NULL) {
            error_handler.HandleError("訂單管理器未初始化");
            ExpertRemove();
        }

        // 獲取單一交易信號處理器實例
        SignalHandler* signal_handler = dynamic_cast<SignalHandler*>(GetRegistry().GetValue("SignalHandler", NULL));
        if(signal_handler == NULL) {
            error_handler.HandleError("單一交易信號處理器未初始化");
            ExpertRemove();
        }

        // 獲取 MQL開倉 封裝器實例
        MqlOrderWrapper* mql_order_wrapper = dynamic_cast<MqlOrderWrapper*>(GetRegistry().GetValue("MqlOrderWrapper", NULL));
        if(mql_order_wrapper == NULL) {
            error_handler.HandleError("MQL開倉 封裝器未初始化");
            ExpertRemove();
        }

        // 獲取 MQL平倉 封裝器實例
        MqlCloseWrapper* mql_close_wrapper = dynamic_cast<MqlCloseWrapper*>(GetRegistry().GetValue("MqlCloseWrapper", NULL));
        if(mql_close_wrapper == NULL) {
            error_handler.HandleError("MQL平倉 封裝器未初始化");
            ExpertRemove();
        }

        MqlOrder order_config;
        MqlClose close_config;
        mql_order_wrapper.GetOrder(order_config);
        mql_close_wrapper.GetClose(close_config);

        // 如果有買入或賣出信號
        if(signal_handler.GetSignal() == SIGNAL_BUY || signal_handler.GetSignal() == SIGNAL_SELL){
            // 交易執行
            int ticket = order_manager.market(order_config.type, order_config.volume, order_config.stoploss, order_config.takeprofit, order_config.comment);
            if(ticket < 0)
            {
                error_handler.HandleError("交易執行失敗");
            }
        }

        // 如果有平倉信號
        if(signal_handler.GetSignal() == SIGNAL_CLOSE)
        {
            // 平倉
            bool result = order_manager.close(close_config.ticket);
            if(!result)
            {
                error_handler.HandleError("平倉失敗");
            }
        }


        return true;
    }
}trade_execution_stage;